/**
 * 边缘计算节点测试
 * 批次0.2：测试46个边缘计算节点的注册、创建、连接和执行功能
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { EdgeComputingNodesRegistry } from '../../src/visual-script/registry/EdgeComputingNodesRegistry';
import { NodeRegistry } from '../../src/visual-script/nodes/NodeRegistry';

describe('边缘计算节点测试', () => {
  let edgeComputingRegistry: EdgeComputingNodesRegistry;
  let nodeRegistry: NodeRegistry;

  beforeEach(() => {
    // 重置单例实例
    (EdgeComputingNodesRegistry as any).instance = undefined;
    (NodeRegistry as any).instance = undefined;
    
    // 创建新实例
    edgeComputingRegistry = EdgeComputingNodesRegistry.getInstance();
    nodeRegistry = NodeRegistry.getInstance();
  });

  describe('边缘计算节点注册表', () => {
    it('应该正确初始化边缘计算节点注册表', () => {
      expect(edgeComputingRegistry).toBeDefined();
      expect(edgeComputingRegistry.getRegisteredNodeCount()).toBe(46);
    });

    it('应该注册所有46个边缘计算节点', () => {
      const registeredTypes = edgeComputingRegistry.getRegisteredNodeTypes();
      expect(registeredTypes).toHaveLength(46);
      
      // 验证边缘路由节点 (6个)
      expect(registeredTypes).toContain('EdgeRoutingNode');
      expect(registeredTypes).toContain('EdgeLoadBalancingNode');
      expect(registeredTypes).toContain('EdgeCachingNode');
      expect(registeredTypes).toContain('EdgeCompressionNode');
      expect(registeredTypes).toContain('EdgeOptimizationNode');
      expect(registeredTypes).toContain('EdgeQoSNode');
      
      // 验证云边协调节点 (8个)
      expect(registeredTypes).toContain('CloudEdgeOrchestrationNode');
      expect(registeredTypes).toContain('HybridComputingNode');
      expect(registeredTypes).toContain('DataSynchronizationNode');
      expect(registeredTypes).toContain('TaskDistributionNode');
      expect(registeredTypes).toContain('ResourceOptimizationNode');
      expect(registeredTypes).toContain('LatencyOptimizationNode');
      expect(registeredTypes).toContain('BandwidthOptimizationNode');
      expect(registeredTypes).toContain('CostOptimizationNode');
      
      // 验证5G网络节点 (8个)
      expect(registeredTypes).toContain('5GConnectionNode');
      expect(registeredTypes).toContain('5GSlicingNode');
      expect(registeredTypes).toContain('5GQoSNode');
      expect(registeredTypes).toContain('5GLatencyNode');
      expect(registeredTypes).toContain('5GBandwidthNode');
      expect(registeredTypes).toContain('5GSecurityNode');
      expect(registeredTypes).toContain('5GMonitoringNode');
      expect(registeredTypes).toContain('5GOptimizationNode');
    });

    it('应该正确检查节点注册状态', () => {
      expect(edgeComputingRegistry.isNodeRegistered('EdgeRoutingNode')).toBe(true);
      expect(edgeComputingRegistry.isNodeRegistered('CloudEdgeOrchestrationNode')).toBe(true);
      expect(edgeComputingRegistry.isNodeRegistered('5GConnectionNode')).toBe(true);
      expect(edgeComputingRegistry.isNodeRegistered('NonExistentNode')).toBe(false);
    });

    it('应该返回正确的统计信息', () => {
      const stats = edgeComputingRegistry.getStatistics();
      expect(stats.totalNodes).toBe(46);
      expect(stats.edgeRoutingNodes).toBe(6);
      expect(stats.cloudEdgeNodes).toBe(8);
      expect(stats.fiveGNodes).toBe(8);
      expect(stats.edgeDeviceNodes).toBe(24);
      expect(stats.registeredAt).toBeDefined();
    });
  });

  describe('节点创建和执行', () => {
    it('应该能够创建边缘路由节点', () => {
      const node = nodeRegistry.createNode('EdgeRoutingNode');
      expect(node).toBeDefined();
      expect(node?.type).toBe('EdgeRoutingNode');
    });

    it('应该能够创建云边协调节点', () => {
      const node = nodeRegistry.createNode('CloudEdgeOrchestrationNode');
      expect(node).toBeDefined();
      expect(node?.type).toBe('CloudEdgeOrchestrationNode');
    });

    it('应该能够创建5G网络节点', () => {
      const node = nodeRegistry.createNode('5GConnectionNode');
      expect(node).toBeDefined();
      expect(node?.type).toBe('5GConnectionNode');
    });

    it('应该能够创建边缘设备节点', () => {
      const node = nodeRegistry.createNode('EdgeDeviceRegistrationNode');
      expect(node).toBeDefined();
      expect(node?.type).toBe('EdgeDeviceRegistrationNode');
    });
  });

  describe('节点分类', () => {
    it('应该正确设置节点分类', () => {
      const categories = nodeRegistry.getNodeCategories();
      expect(categories.has('Edge/Routing')).toBe(true);
      expect(categories.has('Edge/CloudEdge')).toBe(true);
      expect(categories.has('Edge/5G')).toBe(true);
      expect(categories.has('Edge/Device')).toBe(true);
    });

    it('应该正确分配节点到分类', () => {
      const routingNodes = nodeRegistry.getNodesByCategory('Edge/Routing');
      expect(routingNodes).toContain('EdgeRoutingNode');
      expect(routingNodes).toContain('EdgeLoadBalancingNode');
      
      const cloudEdgeNodes = nodeRegistry.getNodesByCategory('Edge/CloudEdge');
      expect(cloudEdgeNodes).toContain('CloudEdgeOrchestrationNode');
      expect(cloudEdgeNodes).toContain('HybridComputingNode');
      
      const fiveGNodes = nodeRegistry.getNodesByCategory('Edge/5G');
      expect(fiveGNodes).toContain('5GConnectionNode');
      expect(fiveGNodes).toContain('5GSlicingNode');
    });
  });

  describe('节点信息', () => {
    it('应该返回正确的节点信息', () => {
      const nodeInfo = nodeRegistry.getNodeInfo('EdgeRoutingNode');
      expect(nodeInfo).toBeDefined();
      expect(nodeInfo.type).toBe('EdgeRoutingNode');
      expect(nodeInfo.name).toBe('边缘路由');
      expect(nodeInfo.description).toBeDefined();
    });

    it('应该返回所有节点信息', () => {
      const allNodeInfo = nodeRegistry.getAllNodeInfo();
      expect(allNodeInfo.length).toBeGreaterThanOrEqual(46);
      
      const edgeRoutingInfo = allNodeInfo.find(info => info.type === 'EdgeRoutingNode');
      expect(edgeRoutingInfo).toBeDefined();
      expect(edgeRoutingInfo.name).toBe('边缘路由');
    });
  });

  describe('节点执行', () => {
    it('应该能够执行边缘路由节点', async () => {
      const node = nodeRegistry.createNode('EdgeRoutingNode');
      expect(node).toBeDefined();
      
      if (node && typeof node.execute === 'function') {
        const inputs = {
          clientInfo: { location: { latitude: 39.9042, longitude: 116.4074 } },
          routingPolicy: 'latency',
          edgeNodes: [
            { nodeId: 'edge1', status: 'active', latency: 10, load: 30 },
            { nodeId: 'edge2', status: 'active', latency: 20, load: 50 }
          ],
          networkMetrics: {}
        };
        
        const outputs = await node.execute(inputs);
        expect(outputs).toBeDefined();
        expect(outputs.selectedNode).toBeDefined();
        expect(outputs.routingDecision).toBeDefined();
        expect(outputs.routingMetrics).toBeDefined();
      }
    });

    it('应该能够执行云边协调节点', async () => {
      const node = nodeRegistry.createNode('CloudEdgeOrchestrationNode');
      expect(node).toBeDefined();
      
      if (node && typeof node.execute === 'function') {
        const inputs = {
          cloudResources: [
            { id: 'cloud1', type: 'compute', capacity: 100 }
          ],
          edgeNodes: [
            { id: 'edge1', type: 'edge_server', capacity: 50 }
          ],
          workloads: [
            { id: 'workload1', type: 'compute', requirements: { cpu: 2, memory: 4 } }
          ],
          orchestrationPolicy: { strategy: 'latency_aware' }
        };
        
        const outputs = await node.execute(inputs);
        expect(outputs).toBeDefined();
        expect(outputs.orchestrationPlan).toBeDefined();
        expect(outputs.resourceAllocation).toBeDefined();
        expect(outputs.orchestrationMetrics).toBeDefined();
      }
    });

    it('应该能够执行5G连接节点', async () => {
      const node = nodeRegistry.createNode('5GConnectionNode');
      expect(node).toBeDefined();
      
      if (node && typeof node.execute === 'function') {
        const inputs = {
          deviceInfo: { 
            deviceId: 'device123',
            deviceType: 'smartphone',
            location: { latitude: 39.9042, longitude: 116.4074 }
          },
          connectionType: 'eMBB',
          networkSlice: 'default',
          qosRequirements: { latency: 10, bandwidth: 100 }
        };
        
        const outputs = await node.execute(inputs);
        expect(outputs).toBeDefined();
        expect(outputs.connectionStatus).toBeDefined();
        expect(outputs.networkMetrics).toBeDefined();
        expect(outputs.connectionId).toBeDefined();
      }
    });
  });

  describe('错误处理', () => {
    it('应该正确处理不存在的节点类型', () => {
      const node = nodeRegistry.createNode('NonExistentEdgeNode');
      expect(node).toBeNull();
    });

    it('应该正确处理无效的节点输入', async () => {
      const node = nodeRegistry.createNode('EdgeRoutingNode');
      expect(node).toBeDefined();
      
      if (node && typeof node.execute === 'function') {
        // 测试无效输入
        await expect(node.execute({})).rejects.toThrow();
      }
    });
  });

  describe('性能测试', () => {
    it('应该能够快速创建大量节点', () => {
      const startTime = Date.now();
      const nodes = [];
      
      for (let i = 0; i < 100; i++) {
        const node = nodeRegistry.createNode('EdgeRoutingNode');
        if (node) {
          nodes.push(node);
        }
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      expect(nodes.length).toBe(100);
      expect(duration).toBeLessThan(1000); // 应该在1秒内完成
    });

    it('应该能够快速查询节点信息', () => {
      const startTime = Date.now();
      
      for (let i = 0; i < 1000; i++) {
        const nodeInfo = nodeRegistry.getNodeInfo('EdgeRoutingNode');
        expect(nodeInfo).toBeDefined();
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      expect(duration).toBeLessThan(100); // 应该在100毫秒内完成
    });
  });
});
