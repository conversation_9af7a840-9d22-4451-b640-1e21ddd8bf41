/**
 * 边缘计算节点注册表
 * 批次0.2：注册46个边缘计算节点到编辑器
 * 包括边缘路由节点、云边协调节点、5G网络节点和边缘设备扩展节点
 */

import { NodeRegistry } from '../nodes/NodeRegistry';

// 导入边缘路由节点 (6个)
import {
  EdgeRoutingNode,
  EdgeLoadBalancingNode,
  EdgeCachingNode,
  EdgeCompressionNode
} from '../nodes/edge/EdgeRoutingNodes';

import {
  EdgeOptimizationNode,
  EdgeQoSNode
} from '../nodes/edge/EdgeRoutingNodes2';

// 导入云边协调节点 (8个)
import {
  CloudEdgeOrchestrationNode,
  HybridComputingNode
} from '../nodes/edge/CloudEdgeNodes';

import {
  DataSynchronizationNode,
  TaskDistributionNode
} from '../nodes/edge/CloudEdgeNodes2';

import {
  ResourceOptimizationNode,
  LatencyOptimizationNode
} from '../nodes/edge/CloudEdgeNodes3';

import {
  BandwidthOptimizationNode,
  CostOptimizationNode
} from '../nodes/edge/CloudEdgeNodes4';

// 导入5G网络节点 (8个)
import {
  FiveGConnectionNode,
  FiveGSlicingNode,
  FiveGQoSNode
} from '../nodes/edge/FiveGNetworkNodes';

import {
  FiveGLatencyNode,
  FiveGBandwidthNode
} from '../nodes/edge/FiveGNetworkNodes2';

import {
  FiveGSecurityNode,
  FiveGMonitoringNode,
  FiveGOptimizationNode
} from '../nodes/edge/FiveGNetworkNodes3';

// 导入边缘设备扩展节点 (24个)
import {
  EdgeDeviceRegistrationNode,
  EdgeDeviceMonitoringNode,
  EdgeDeviceControlNode
} from '../nodes/edge/EdgeDeviceNodes';

import {
  EdgeResourceManagementNode,
  EdgeNetworkNode
} from '../nodes/edge/EdgeDeviceNodes2';

import {
  EdgeSecurityNode,
  EdgeUpdateNode,
  EdgeDiagnosticsNode
} from '../nodes/edge/EdgeDeviceNodes3';

import {
  EdgePerformanceNode,
  EdgeFailoverNode
} from '../nodes/edge/EdgeDeviceNodes4';

import {
  EdgeConfigurationNode,
  EdgeMaintenanceNode,
  EdgeBackupNode
} from '../nodes/edge/EdgeDeviceNodes5';

import {
  EdgeSyncNode,
  EdgeAnalyticsNode
} from '../nodes/edge/EdgeDeviceNodes6';

import {
  EdgeAIInferenceNode,
  EdgeModelDeploymentNode,
  EdgeModelOptimizationNode
} from '../nodes/edge/EdgeAINodes';

import {
  EdgeFederatedLearningNode,
  EdgeAIMonitoringNode
} from '../nodes/edge/EdgeAINodes2';

import {
  EdgeAIPerformanceNode,
  EdgeAISecurityNode,
  EdgeAIAnalyticsNode
} from '../nodes/edge/EdgeAINodes3';

import {
  EdgeModelCacheNode,
  EdgeModelVersioningNode,
  EdgeAIResourceNode,
  EdgeAISchedulerNode
} from '../nodes/edge/EdgeAINodes4';

/**
 * 边缘计算节点注册表类
 */
export class EdgeComputingNodesRegistry {
  private static instance: EdgeComputingNodesRegistry;
  private nodeRegistry: NodeRegistry;
  private registeredNodes: Map<string, any> = new Map();

  private constructor() {
    this.nodeRegistry = NodeRegistry.getInstance();
    this.registerAllEdgeComputingNodes();
  }

  public static getInstance(): EdgeComputingNodesRegistry {
    if (!EdgeComputingNodesRegistry.instance) {
      EdgeComputingNodesRegistry.instance = new EdgeComputingNodesRegistry();
    }
    return EdgeComputingNodesRegistry.instance;
  }

  /**
   * 注册所有边缘计算节点
   */
  private registerAllEdgeComputingNodes(): void {
    console.log('开始注册边缘计算节点...');
    
    this.registerEdgeRoutingNodes();
    this.registerCloudEdgeNodes();
    this.registerFiveGNodes();
    this.registerEdgeDeviceNodes();
    
    console.log(`边缘计算节点注册完成，总计: ${this.registeredNodes.size}个节点`);
  }

  /**
   * 注册边缘路由节点 (6个)
   */
  private registerEdgeRoutingNodes(): void {
    const edgeRoutingNodes = [
      EdgeRoutingNode,
      EdgeLoadBalancingNode,
      EdgeCachingNode,
      EdgeCompressionNode,
      EdgeOptimizationNode,
      EdgeQoSNode
    ];

    const category = 'Edge/Routing';
    
    for (const NodeClass of edgeRoutingNodes) {
      this.nodeRegistry.registerNode(NodeClass, category);
      this.registeredNodes.set(NodeClass.TYPE, NodeClass);
    }

    console.log(`已注册 ${edgeRoutingNodes.length} 个边缘路由节点`);
  }

  /**
   * 注册云边协调节点 (8个)
   */
  private registerCloudEdgeNodes(): void {
    const cloudEdgeNodes = [
      CloudEdgeOrchestrationNode,
      HybridComputingNode,
      DataSynchronizationNode,
      TaskDistributionNode,
      ResourceOptimizationNode,
      LatencyOptimizationNode,
      BandwidthOptimizationNode,
      CostOptimizationNode
    ];

    const category = 'Edge/CloudEdge';
    
    for (const NodeClass of cloudEdgeNodes) {
      this.nodeRegistry.registerNode(NodeClass, category);
      this.registeredNodes.set(NodeClass.TYPE, NodeClass);
    }

    console.log(`已注册 ${cloudEdgeNodes.length} 个云边协调节点`);
  }

  /**
   * 注册5G网络节点 (8个)
   */
  private registerFiveGNodes(): void {
    const fiveGNodes = [
      FiveGConnectionNode,
      FiveGSlicingNode,
      FiveGQoSNode,
      FiveGLatencyNode,
      FiveGBandwidthNode,
      FiveGSecurityNode,
      FiveGMonitoringNode,
      FiveGOptimizationNode
    ];

    const category = 'Edge/5G';
    
    for (const NodeClass of fiveGNodes) {
      this.nodeRegistry.registerNode(NodeClass, category);
      this.registeredNodes.set(NodeClass.TYPE, NodeClass);
    }

    console.log(`已注册 ${fiveGNodes.length} 个5G网络节点`);
  }

  /**
   * 注册边缘设备扩展节点 (24个)
   */
  private registerEdgeDeviceNodes(): void {
    const edgeDeviceNodes = [
      EdgeDeviceRegistrationNode,
      EdgeDeviceMonitoringNode,
      EdgeDeviceControlNode,
      EdgeResourceManagementNode,
      EdgeNetworkNode,
      EdgeSecurityNode,
      EdgeUpdateNode,
      EdgeDiagnosticsNode,
      EdgePerformanceNode,
      EdgeFailoverNode,
      EdgeConfigurationNode,
      EdgeMaintenanceNode,
      EdgeBackupNode,
      EdgeSyncNode,
      EdgeAnalyticsNode,
      EdgeAIInferenceNode,
      EdgeModelDeploymentNode,
      EdgeModelOptimizationNode,
      EdgeFederatedLearningNode,
      EdgeAIMonitoringNode,
      EdgeAIPerformanceNode,
      EdgeAISecurityNode,
      EdgeAIAnalyticsNode,
      EdgeModelCacheNode,
      EdgeModelVersioningNode,
      EdgeAIResourceNode,
      EdgeAISchedulerNode
    ];

    const category = 'Edge/Device';
    
    for (const NodeClass of edgeDeviceNodes) {
      this.nodeRegistry.registerNode(NodeClass, category);
      this.registeredNodes.set(NodeClass.TYPE, NodeClass);
    }

    console.log(`已注册 ${edgeDeviceNodes.length} 个边缘设备扩展节点`);
  }

  /**
   * 获取已注册的节点数量
   */
  public getRegisteredNodeCount(): number {
    return this.registeredNodes.size;
  }

  /**
   * 获取所有已注册的节点类型
   */
  public getRegisteredNodeTypes(): string[] {
    return Array.from(this.registeredNodes.keys());
  }

  /**
   * 检查节点是否已注册
   */
  public isNodeRegistered(nodeType: string): boolean {
    return this.registeredNodes.has(nodeType);
  }

  /**
   * 获取节点统计信息
   */
  public getStatistics(): any {
    return {
      totalNodes: this.registeredNodes.size,
      edgeRoutingNodes: 6,
      cloudEdgeNodes: 8,
      fiveGNodes: 8,
      edgeDeviceNodes: 24,
      registeredAt: new Date().toISOString()
    };
  }
}

// 导出单例实例
export const edgeComputingNodesRegistry = EdgeComputingNodesRegistry.getInstance();

// 初始化日志
console.log('DL引擎边缘计算节点注册表已初始化');
console.log('批次0.2边缘计算节点：46个');
console.log('  - 边缘路由节点：6个');
console.log('  - 云边协调节点：8个');
console.log('  - 5G网络节点：8个');
console.log('  - 边缘设备扩展节点：24个');

const stats = edgeComputingNodesRegistry.getStatistics();
console.log('边缘计算节点统计信息：', stats);
