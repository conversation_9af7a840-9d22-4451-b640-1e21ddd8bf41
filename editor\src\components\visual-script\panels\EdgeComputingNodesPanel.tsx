/**
 * 边缘计算节点面板
 * 批次0.2：提供46个边缘计算节点的分类展示和拖拽功能
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Collapse,
  List,
  Input,
  Space,
  Tag,
  Typography,
  Badge,
  Divider,
  Spin,
  Tooltip,
  Button
} from 'antd';
import {
  SearchOutlined,
  CloudServerOutlined,
  ApiOutlined,
  WifiOutlined,
  SettingOutlined,
  FilterOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Text, Title } = Typography;
const { Panel } = Collapse;
const { Search } = Input;

/**
 * 边缘计算节点分类
 */
export enum EdgeComputingNodeCategory {
  EDGE_ROUTING = 'Edge/Routing',
  CLOUD_EDGE = 'Edge/CloudEdge',
  FIVE_G = 'Edge/5G',
  EDGE_DEVICE = 'Edge/Device'
}

/**
 * 边缘计算节点面板属性
 */
export interface EdgeComputingNodesPanelProps {
  onNodeSelect?: (nodeType: string) => void;
  onNodeAdd?: (nodeType: string) => void;
  visible?: boolean;
  loading?: boolean;
}

/**
 * 节点项接口
 */
interface NodeItem {
  type: string;
  name: string;
  description: string;
  category: EdgeComputingNodeCategory;
  icon: string;
  color: string;
  tags: string[];
}

export const EdgeComputingNodesPanel: React.FC<EdgeComputingNodesPanelProps> = ({
  onNodeSelect,
  onNodeAdd,
  visible = true,
  loading = false
}) => {
  const { t } = useTranslation();
  const [expandedPanels, setExpandedPanels] = useState<string[]>([]);
  const [nodeItems, setNodeItems] = useState<NodeItem[]>([]);
  const [filteredNodes, setFilteredNodes] = useState<NodeItem[]>([]);
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // 初始化节点数据
  useEffect(() => {
    const initializeNodes = () => {
      const nodes: NodeItem[] = [
        // 边缘路由节点 (6个)
        {
          type: 'EdgeRoutingNode',
          name: '边缘路由',
          description: '提供智能边缘路由决策功能',
          category: EdgeComputingNodeCategory.EDGE_ROUTING,
          icon: '🔀',
          color: '#1890ff',
          tags: ['路由', '智能']
        },
        {
          type: 'EdgeLoadBalancingNode',
          name: '边缘负载均衡',
          description: '实现边缘节点间的负载均衡',
          category: EdgeComputingNodeCategory.EDGE_ROUTING,
          icon: '⚖️',
          color: '#1890ff',
          tags: ['负载均衡', '性能']
        },
        {
          type: 'EdgeCachingNode',
          name: '边缘缓存',
          description: '提供边缘缓存管理功能',
          category: EdgeComputingNodeCategory.EDGE_ROUTING,
          icon: '💾',
          color: '#1890ff',
          tags: ['缓存', '存储']
        },
        {
          type: 'EdgeCompressionNode',
          name: '边缘压缩',
          description: '数据压缩和解压缩处理',
          category: EdgeComputingNodeCategory.EDGE_ROUTING,
          icon: '🗜️',
          color: '#1890ff',
          tags: ['压缩', '优化']
        },
        {
          type: 'EdgeOptimizationNode',
          name: '边缘优化',
          description: '边缘计算性能优化',
          category: EdgeComputingNodeCategory.EDGE_ROUTING,
          icon: '⚡',
          color: '#1890ff',
          tags: ['优化', '性能']
        },
        {
          type: 'EdgeQoSNode',
          name: '边缘服务质量',
          description: '服务质量管理和控制',
          category: EdgeComputingNodeCategory.EDGE_ROUTING,
          icon: '🎯',
          color: '#1890ff',
          tags: ['QoS', '质量']
        },

        // 云边协调节点 (8个)
        {
          type: 'CloudEdgeOrchestrationNode',
          name: '云边协调',
          description: '云端和边缘节点协调管理',
          category: EdgeComputingNodeCategory.CLOUD_EDGE,
          icon: '☁️',
          color: '#52c41a',
          tags: ['协调', '管理']
        },
        {
          type: 'HybridComputingNode',
          name: '混合计算',
          description: '云边混合计算资源调度',
          category: EdgeComputingNodeCategory.CLOUD_EDGE,
          icon: '🔄',
          color: '#52c41a',
          tags: ['混合', '计算']
        },
        {
          type: 'DataSynchronizationNode',
          name: '数据同步',
          description: '云边数据同步管理',
          category: EdgeComputingNodeCategory.CLOUD_EDGE,
          icon: '🔄',
          color: '#52c41a',
          tags: ['同步', '数据']
        },
        {
          type: 'TaskDistributionNode',
          name: '任务分发',
          description: '计算任务智能分发',
          category: EdgeComputingNodeCategory.CLOUD_EDGE,
          icon: '📤',
          color: '#52c41a',
          tags: ['分发', '任务']
        },
        {
          type: 'ResourceOptimizationNode',
          name: '资源优化',
          description: '云边资源优化配置',
          category: EdgeComputingNodeCategory.CLOUD_EDGE,
          icon: '⚙️',
          color: '#52c41a',
          tags: ['资源', '优化']
        },
        {
          type: 'LatencyOptimizationNode',
          name: '延迟优化',
          description: '网络延迟优化管理',
          category: EdgeComputingNodeCategory.CLOUD_EDGE,
          icon: '⏱️',
          color: '#52c41a',
          tags: ['延迟', '优化']
        },
        {
          type: 'BandwidthOptimizationNode',
          name: '带宽优化',
          description: '网络带宽优化管理',
          category: EdgeComputingNodeCategory.CLOUD_EDGE,
          icon: '📊',
          color: '#52c41a',
          tags: ['带宽', '优化']
        },
        {
          type: 'CostOptimizationNode',
          name: '成本优化',
          description: '云边计算成本优化',
          category: EdgeComputingNodeCategory.CLOUD_EDGE,
          icon: '💰',
          color: '#52c41a',
          tags: ['成本', '优化']
        },

        // 5G网络节点 (8个)
        {
          type: '5GConnectionNode',
          name: '5G连接',
          description: '5G网络连接管理',
          category: EdgeComputingNodeCategory.FIVE_G,
          icon: '📶',
          color: '#722ed1',
          tags: ['5G', '连接']
        },
        {
          type: '5GSlicingNode',
          name: '5G网络切片',
          description: '5G网络切片管理',
          category: EdgeComputingNodeCategory.FIVE_G,
          icon: '🍰',
          color: '#722ed1',
          tags: ['5G', '切片']
        },
        {
          type: '5GQoSNode',
          name: '5G服务质量',
          description: '5G网络服务质量管理',
          category: EdgeComputingNodeCategory.FIVE_G,
          icon: '🎯',
          color: '#722ed1',
          tags: ['5G', 'QoS']
        },
        {
          type: '5GLatencyNode',
          name: '5G延迟管理',
          description: '5G网络延迟管理',
          category: EdgeComputingNodeCategory.FIVE_G,
          icon: '⚡',
          color: '#722ed1',
          tags: ['5G', '延迟']
        },
        {
          type: '5GBandwidthNode',
          name: '5G带宽管理',
          description: '5G网络带宽管理',
          category: EdgeComputingNodeCategory.FIVE_G,
          icon: '📈',
          color: '#722ed1',
          tags: ['5G', '带宽']
        },
        {
          type: '5GSecurityNode',
          name: '5G安全',
          description: '5G网络安全管理',
          category: EdgeComputingNodeCategory.FIVE_G,
          icon: '🔒',
          color: '#722ed1',
          tags: ['5G', '安全']
        },
        {
          type: '5GMonitoringNode',
          name: '5G监控',
          description: '5G网络监控管理',
          category: EdgeComputingNodeCategory.FIVE_G,
          icon: '📊',
          color: '#722ed1',
          tags: ['5G', '监控']
        },
        {
          type: '5GOptimizationNode',
          name: '5G优化',
          description: '5G网络性能优化',
          category: EdgeComputingNodeCategory.FIVE_G,
          icon: '⚡',
          color: '#722ed1',
          tags: ['5G', '优化']
        }
      ];

      setNodeItems(nodes);
      setFilteredNodes(nodes);
    };

    initializeNodes();
  }, []);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchText(value);
    filterNodes(value, selectedCategory);
  };

  // 处理分类过滤
  const handleCategoryFilter = (category: string) => {
    setSelectedCategory(category);
    filterNodes(searchText, category);
  };

  // 过滤节点
  const filterNodes = (search: string, category: string) => {
    let filtered = nodeItems;

    // 分类过滤
    if (category !== 'all') {
      filtered = filtered.filter(node => node.category === category);
    }

    // 搜索过滤
    if (search) {
      const searchLower = search.toLowerCase();
      filtered = filtered.filter(node =>
        node.name.toLowerCase().includes(searchLower) ||
        node.description.toLowerCase().includes(searchLower) ||
        node.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    setFilteredNodes(filtered);
  };

  // 处理节点点击
  const handleNodeClick = (node: NodeItem) => {
    if (onNodeSelect) {
      onNodeSelect(node.type);
    }
  };

  // 处理节点添加
  const handleNodeAdd = (node: NodeItem) => {
    if (onNodeAdd) {
      onNodeAdd(node.type);
    }
  };

  if (!visible) {
    return null;
  }

  return (
    <Card
      title={
        <Space>
          <CloudServerOutlined />
          <Title level={4} style={{ margin: 0 }}>
            边缘计算节点
          </Title>
          <Badge count={filteredNodes.length} style={{ backgroundColor: '#1890ff' }} />
        </Space>
      }
      size="small"
      style={{ height: '100%', overflow: 'hidden' }}
      bodyStyle={{ padding: '12px', height: 'calc(100% - 57px)', overflow: 'auto' }}
    >
      <Spin spinning={loading}>
        {/* 搜索和过滤 */}
        <Space direction="vertical" style={{ width: '100%', marginBottom: 12 }}>
          <Search
            placeholder="搜索边缘计算节点..."
            allowClear
            onSearch={handleSearch}
            onChange={(e) => handleSearch(e.target.value)}
            prefix={<SearchOutlined />}
          />
          
          <Space wrap>
            <Button
              size="small"
              type={selectedCategory === 'all' ? 'primary' : 'default'}
              onClick={() => handleCategoryFilter('all')}
            >
              全部
            </Button>
            <Button
              size="small"
              type={selectedCategory === EdgeComputingNodeCategory.EDGE_ROUTING ? 'primary' : 'default'}
              onClick={() => handleCategoryFilter(EdgeComputingNodeCategory.EDGE_ROUTING)}
            >
              边缘路由
            </Button>
            <Button
              size="small"
              type={selectedCategory === EdgeComputingNodeCategory.CLOUD_EDGE ? 'primary' : 'default'}
              onClick={() => handleCategoryFilter(EdgeComputingNodeCategory.CLOUD_EDGE)}
            >
              云边协调
            </Button>
            <Button
              size="small"
              type={selectedCategory === EdgeComputingNodeCategory.FIVE_G ? 'primary' : 'default'}
              onClick={() => handleCategoryFilter(EdgeComputingNodeCategory.FIVE_G)}
            >
              5G网络
            </Button>
          </Space>
        </Space>

        <Divider style={{ margin: '8px 0' }} />

        {/* 节点列表 */}
        <List
          size="small"
          dataSource={filteredNodes}
          renderItem={(node) => (
            <List.Item
              key={node.type}
              style={{
                padding: '8px 12px',
                cursor: 'pointer',
                borderRadius: '4px',
                marginBottom: '4px',
                border: '1px solid #f0f0f0',
                transition: 'all 0.2s'
              }}
              onClick={() => handleNodeClick(node)}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = '#f5f5f5';
                e.currentTarget.style.borderColor = node.color;
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.borderColor = '#f0f0f0';
              }}
            >
              <List.Item.Meta
                avatar={
                  <div
                    style={{
                      width: 32,
                      height: 32,
                      borderRadius: '50%',
                      backgroundColor: node.color,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white',
                      fontSize: '14px'
                    }}
                  >
                    {node.icon}
                  </div>
                }
                title={
                  <Space>
                    <Text strong style={{ fontSize: '13px' }}>
                      {node.name}
                    </Text>
                    {node.tags.length > 0 && (
                      <Tag size="small" color={node.color}>
                        {node.tags[0]}
                      </Tag>
                    )}
                  </Space>
                }
                description={
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {node.description.length > 50 
                      ? `${node.description.substring(0, 50)}...` 
                      : node.description
                    }
                  </Text>
                }
              />
              <Tooltip title="添加到画布">
                <Button
                  type="text"
                  size="small"
                  icon={<InfoCircleOutlined />}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleNodeAdd(node);
                  }}
                />
              </Tooltip>
            </List.Item>
          )}
        />
      </Spin>
    </Card>
  );
};

export default EdgeComputingNodesPanel;
